<form class="main-content-wrapper"
      novalidate
      data-ng-controller='openingsRenameModalController as vm'
      style="min-width:700px; max-width: 700px;">

    <div class="widget widget-header-vertically-expand"
         ng-form="openingsRenameModalForm"
         ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-is-modal="true"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-content
             data-is-modal="true">

            <md-card>

                <md-card-content>

                    <!-- Description (Editable) -->
                    <md-input-container class="md-block" flex="100">
                        <label>Description</label>
                        <input ng-disabled="vm.disabled"
                               ng-required="true"
                               ng-model="vm.data.overrideDisplayDescription" />
                    </md-input-container>

                    <!-- Original Description (Disabled) -->
                    <div style="display: grid; grid-template-columns: 1fr 100px;">
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Original Description</label>
                            <input ng-disabled="true"
                                   ng-model="vm.data.originalDescription" />
                        </md-input-container>
                        <md-button class="md-raised"
                                   ng-disabled="vm.disabled"
                                   ng-click="vm.data.overrideDisplayDescription = vm.data.originalDescription;">
                            RESTORE
                        </md-button>
                    </div>

                    <!-- Opening Style (Editable) -->
                    <md-input-container class="md-block" flex="100">
                        <label>Opening Style</label>
                        <md-select ng-required="true"
                                   ng-disabled="vm.disabled"
                                   ng-model="vm.data.overrideOpeningStyle"
                                   ng-model-options="{trackBy: '$value.openingStyleCode'}">
                            <md-option ng-value="style"
                                       ng-repeat="style in vm.openingStyleList">
                                {{style.title}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Original Opening Style (Disabled) -->
                    <div style="display: grid; grid-template-columns: 1fr 100px;">
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Original Opening Style</label>
                            <input ng-disabled="true"
                                   ng-model="vm.data.originalOpeningStyle.title" />
                        </md-input-container>
                        <md-button class="md-raised"
                                   ng-disabled="vm.disabled"
                                   ng-click="vm.data.overrideOpeningStyle = vm.data.originalOpeningStyle;">
                            RESTORE
                        </md-button>
                    </div>

                    <!-- Glass (Editable) -->
                    <md-input-container class="md-block" flex="100">
                        <label>Glass</label>
                        <input ng-disabled="vm.disabled"
                               ng-required="true"
                               ng-model="vm.data.overrideGlass" />
                    </md-input-container>

                    <!-- Original Glass (Disabled) -->
                    <div style="display: grid; grid-template-columns: 1fr 100px;">
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Original Glass</label>
                            <input ng-disabled="true"
                                   ng-model="vm.data.originalGlass" />
                        </md-input-container>
                        <md-button class="md-raised"
                                   ng-disabled="vm.disabled"
                                   ng-click="vm.data.overrideGlass = vm.data.originalGlass;">
                            RESTORE
                        </md-button>
                    </div>

                    <!-- U Value (Editable) -->
                    <md-input-container class="md-block" flex="100">
                        <label>U Value</label>
                        <input ng-disabled="vm.disabled"
                               ng-required="true"
                               type="number"
                               step="0.01"
                               ng-model="vm.data.overrideUValue" />
                    </md-input-container>

                    <!-- Original U Value (Disabled) -->
                    <div style="display: grid; grid-template-columns: 1fr 100px;">
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Original U Value</label>
                            <input ng-disabled="true"
                                   ng-model="vm.data.originalUValue" />
                        </md-input-container>
                        <md-button class="md-raised"
                                   ng-disabled="vm.disabled"
                                   ng-click="vm.data.overrideUValue = vm.data.originalUValue;">
                            RESTORE
                        </md-button>
                    </div>

                    <!-- SHGC (Editable) -->
                    <md-input-container class="md-block" flex="100">
                        <label>SHGC</label>
                        <input ng-disabled="vm.disabled"
                               ng-required="true"
                               type="number"
                               step="0.01"
                               ng-model="vm.data.overrideSHGC" />
                    </md-input-container>

                    <!-- Original SHGC (Disabled) -->
                    <div style="display: grid; grid-template-columns: 1fr 100px;">
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Original SHGC</label>
                            <input ng-disabled="true"
                                   ng-model="vm.data.originalSHGC" />
                        </md-input-container>
                        <md-button class="md-raised"
                                   ng-disabled="vm.disabled"
                                   ng-click="vm.data.overrideSHGC = vm.data.originalSHGC;">
                            RESTORE
                        </md-button>
                    </div>

                </md-card-content>

            </md-card>

            <!-- Action Buttons -->
            <div data-cc-widget-button-bar
                 data-is-modal="true">

                <md-button class="md-raised"
                           ng-disabled="vm.disabled"
                           ng-click="vm.restoreAll()">
                    RESTORE ALL
                </md-button>
                <md-button class="md-raised md-primary"
                           redi-allow-roles="['assessment_page_(tabs/sub-tabs)__openings__edit']"
                           ng-disabled="openingsRenameModalForm.$invalid || vm.disabled"
                           ng-click="vm.save()">
                    SAVE
                </md-button>
                <md-button class="md-raised"
                           ng-click="vm.cancel()">
                    CANCEL
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>

</form>
