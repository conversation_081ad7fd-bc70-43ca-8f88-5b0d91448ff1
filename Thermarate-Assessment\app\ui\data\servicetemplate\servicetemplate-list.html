<section id="construction-list-view"
         class="main-content-wrapper"
         data-ng-controller="ServiceTemplateListCtrl as vm">

    <div class="widget">

        <!-- Title + Upload Spreadsheet Widget -->
        <div layout="row"
             style="padding: 10px 0px; ">
            <h1 style="margin: auto 0px; font-size: 20px; line-height: 28px; font-weight: 400;">
                {{vm.title}}
            </h1>
            <div layout-align="center center"
                 style="margin-top: 4px; margin-left: 20px;">
                <md-button class="md-primary"
                           ngf-select="vm.uploadFile($file)"
                           redi-allow-roles="['settings__uploaddatasets__view']">
                    Upload Spreadsheet
                </md-button>
                <md-button class="md-primary"
                           ng-click="vm.exportDatabase()"
                           redi-allow-roles="['settings__uploaddatasets__view']">
                    Export
                </md-button>
            </div>
        </div>

        <div data-cc-widget-action-bar
             data-quick-find-model='vm.listFilter'
             data-quick-find-holder="Search"
             data-action-buttons='vm.actionButtons'
             data-refresh-list='vm.refreshList(value)'
             data-spinner-busy='vm.isBusy'
             data-filter-changed="vm.refreshList(value)"
             data-query-builder-model="vm.queryModel"
             data-query-builder-name="Construction"
             data-query-builder-current="vm.currentQuery"
             data-default-start="vm.rptDateRange"
             data-date-range-label="Created"
             data-date-ranges="vm.ranges"
             data-custom-filter="vm.addCustomFilter()">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <md-toolbar class="customFiltersBar" layout="row" flex="100" ng-if="vm.customFilter">
                <div>Filter has been applied.&nbsp;</div>
                <div ng-repeat="item in vm.customFilter"
                     class="customFilter"
                     ng-click="vm.removeCustomFilter(item)">
                    <span ng-if="$index!=0">&nbsp;and&nbsp;</span><span class="blueCustomFilter">{{vm.createCustomFilterLabel(item.field)}}</span> is <span class="blackCustomFilter">{{vm.toSplitTitleCase(item.value)}}</span>
                </div>
            </md-toolbar>
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.serviceTemplateList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="constructionList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="vm.bulkSelected"
                                             ng-click="vm.bulkSelect(!vm.bulkSelected);"></md-checkbox>
                            </div>
                        </th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="serviceCategoryTitle" class="can-sort text-center">Category</th>
                        <th st-sort="manufacturer" class="can-sort text-center">Manufacturer</th>
                        <th st-sort="isFavourite" class="can-sort text-center" style="width: 1%; white-space: nowrap;">Favourite</th>
                        <th style="width: 50px;"></th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.serviceTemplateList" class="list-row clickable">
                        <td>
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="row.isBulkSelected"
                                             ng-click="$event.stopPropagation();">
                                </md-checkbox>
                            </div>
                        </td>
                        <td data-title="Description" ng-click="vm.goToServiceTemplate(row.serviceTemplateId)">
                            <div style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                {{::row.description }}
                                <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                            </div>
                        </td>
                        <td data-title="Type"
                            class="text-center"
                            ng-click="vm.goToServiceTemplate(row.serviceTemplateId)">
                            {{::row.serviceCategoryTitle}}
                        </td>
                        <td data-title="Manufacturer"
                            class="text-center"
                            ng-click="vm.goToServiceTemplate(row.serviceTemplateId)">
                            {{::row.manufacturerDescription}}
                        </td>
                        <td data-title="Favourite" class="text-center">
                            <div style="display: flex; justify-content: center; align-items: center;">
                                <md-checkbox ng-model="row.isFavourite"
                                             style="margin: 0;"
                                             ng-click="vm.setFavouriteStatus(row.serviceTemplateId, !row.isFavourite);$event.stopPropagation();"/>
                            </div>
                        </td>
                        <td class="text-center">
                            <div style="display: flex; justify-content: center; align-items: center;">
                                <md-menu>
                                    <img md-menu-origin
                                         class="clickable"
                                         ng-click="$mdOpenMenu(); $event.stopPropagation();"
                                         src="/content/feather/more-horizontal.svg"/>
                                    <md-menu-content>
                                        <!-- Duplicate -->
                                        <md-menu-item>
                                            <md-button ng-click="vm.clone(row)">
                                                Duplicate
                                            </md-button>
                                        </md-menu-item>
                                        <md-menu-divider></md-menu-divider>
                                        <!-- Delete -->
                                        <md-menu-item>
                                            <md-button ng-click="vm.delete(row)">
                                                <span style="color: orangered;">Delete</span>
                                            </md-button>
                                        </md-menu-item>
                                    </md-menu-content>
                                </md-menu>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="9999" class="text-center">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr;">

                            <!-- Just empty. -->
                            <div></div>

                            <!-- Pagination Display -->
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>

                            <!-- Just empty. -->
                            <div></div>
                        </div>
                    </td>
                </tr>
                </tfoot>
            </table>
            <div class="widget-pager" style="text-align: center;">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: -36px; margin-left: 10px;">
                <!-- Bulk Edit Button -->
                <md-button ng-click="vm.showBulkEditModal()"
                           ng-disabled="!vm.bulkSelectionsExist()"
                           class="md-raised md-primary"
                           style="margin-bottom: 20px;">
                    BULK EDIT
                </md-button>

                <div></div>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>
    .list-row {
        height: 52px;
    }

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

    .go-to-variation-button:hover {
        background-color: #d1d1d1;
    }

    .go-to-variation-button > img {
        position: absolute;
        top: 50%;
        left: 54%;
        transform: translate(-50%, -50%);
        width: 60%;
        height: auto;
    }
</style>
