(function () {

    'use strict';

    let controllerId = 'openingsRenameModalController';

    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q',
            'bootstrap.dialog', 'uuid4',
            'constructionservice',
            'common',
            openingsRenameModalController]);

    function openingsRenameModalController($rootScope, $scope, $mdDialog, $stateParams, $state, $q,
                                      modalDialog, uuid4,
                                      constructionservice,
                                      common) {

        // The model for this form
        const vm = this;

        vm.parent = $scope.parent;
        vm.building = $scope.building;
        vm.option = $scope.option;
        vm.disabled = $scope.disabled;

        function initialize() {

            vm.title = "Rename";

            // Initialize data with current values and original values
            vm.data = {
                // Override fields (editable)
                overrideDisplayDescription: vm.parent.overrideDisplayDescription || vm.parent.displayDescription || vm.parent.description,
                overrideOpeningStyle: vm.parent.overrideOpeningStyle || vm.parent.openingStyle,
                overrideGlass: getOverrideGlass() || getOriginalGlass(),
                overrideUValue: getOverrideUValue() || getOriginalUValue(),
                overrideSHGC: getOverrideSHGC() || getOriginalSHGC(),

                // Original fields (read-only)
                originalDescription: vm.parent.displayDescription || vm.parent.description,
                originalOpeningStyle: vm.parent.openingStyle,
                originalGlass: getOriginalGlass(),
                originalUValue: getOriginalUValue(),
                originalSHGC: getOriginalSHGC()
            };

            function getOriginalGlass() {
                if (vm.parent.glassData && vm.parent.glassData.description) {
                    return vm.parent.glassData.description;
                }
                return "";
            }

            function getOriginalUValue() {
                if (vm.parent.performance && vm.parent.performance.uValue) {
                    return vm.parent.performance.uValue;
                }
                return null;
            }

            function getOriginalSHGC() {
                if (vm.parent.performance && vm.parent.performance.shgc) {
                    return vm.parent.performance.shgc;
                }
                return null;
            }

            function getOverrideGlass() {
                if (vm.parent.glassData && vm.parent.glassData.overrideDescription) {
                    return vm.parent.glassData.overrideDescription;
                }
                return null;
            }

            function getOverrideUValue() {
                if (vm.parent.performance && vm.parent.performance.overrideUValue) {
                    return vm.parent.performance.overrideUValue;
                }
                return null;
            }

            function getOverrideSHGC() {
                if (vm.parent.performance && vm.parent.performance.overrideSHGC) {
                    return vm.parent.performance.overrideSHGC;
                }
                return null;
            }

            // Load opening style list for dropdown
            constructionservice.getOpeningStyleList()
                .then(data => {
                    vm.openingStyleList = data;
                    // Filter opening styles based on category if needed
                    if (vm.parent.category && vm.parent.category.constructionCategoryCode) {
                        vm.openingStyleList = filterOpeningStylesForCategory(data, vm.parent.category.constructionCategoryCode);
                    }
                });
        }

        function filterOpeningStylesForCategory(openingStyles, categoryCode) {
            // Apply the same filtering logic as in building-construction-data-controller.js
            if (categoryCode === "RoofWindow") {
                return openingStyles.filter(x => x.type === 'roof' || x.type === 'all');
            } else if (categoryCode === 'Skylight') {
                return openingStyles.filter(x => x.openingStyleCode === "Fixed");
            } else {
                return openingStyles.filter(x => x.type === 'wall' || x.type === 'all');
            }
        }

        initialize();

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.save = function () {
            $mdDialog.hide(vm.data);
        }

        vm.restoreAll = function() {
            // Restore all fields to their original values
            vm.data.overrideDisplayDescription = vm.data.originalDescription;
            vm.data.overrideOpeningStyle = vm.data.originalOpeningStyle;
            vm.data.overrideGlass = vm.data.originalGlass;
            vm.data.overrideUValue = vm.data.originalUValue;
            vm.data.overrideSHGC = vm.data.originalSHGC;
        }
    }

})();
